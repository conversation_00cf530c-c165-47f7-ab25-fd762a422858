#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
#include <cassert>
#include <limits>
#include <numeric>
#include <sstream>
#include <queue>

/*
 * Enhanced scheduler for "边缘集群AI推理的分布式任务调度".
 *
 * Key improvements:
 * 1. Hybrid priority scheduling: Users sorted by urgency ratio (time_window / samples)
 *    combined with deadline pressure to balance resource allocation.
 *
 * 2. Load-aware NPU selection: Track estimated load on each NPU and distribute
 *    users across NPUs to avoid bottlenecks while minimizing migrations.
 *
 * 3. Optimized batch sizing: Find the sweet spot between memory efficiency and
 *    processing speed by testing multiple batch sizes within constraints.
 *
 * 4. Accurate completion time estimation: Simulate the actual NPU queue processing
 *    with proper timeline accounting for overlapping requests.
 *
 * 5. Resource contention modeling: Consider the impact of concurrent users on
 *    the same server when estimating completion times.
 */

struct NPULoad {
    int server_id;
    int npu_id;
    long long estimated_finish_time;

    NPULoad(int s, int n) : server_id(s), npu_id(n), estimated_finish_time(0) {}
};

struct UserPriority {
    int user_id;
    double urgency_score;

    UserPriority(int id, double score) : user_id(id), urgency_score(score) {}

    bool operator<(const UserPriority& other) const {
        return urgency_score > other.urgency_score; // Higher urgency first
    }
};

int main() {
    std::ios::sync_with_stdio(false);
    std::cin.tie(nullptr);

    int N;
    if (!(std::cin >> N)) return 0;

    const int MAX_BJ_LIMIT = 1000;
    const int MAX_TI_LIMIT = 300;

    std::vector<int> g(N), k(N), m(N);
    for (int i = 0; i < N; ++i) {
        std::cin >> g[i] >> k[i] >> m[i];
    }

    int M;
    std::cin >> M;

    struct User {
        int s, e, cnt;
        double time_density() const {
            return static_cast<double>(cnt) / (e - s);
        }
        double deadline_pressure(long long current_time = 0) const {
            long long remaining_time = e - std::max(static_cast<long long>(s), current_time);
            return remaining_time > 0 ? static_cast<double>(cnt) / remaining_time : 1e9;
        }
    };
    std::vector<User> users(M);
    for (int i = 0; i < M; ++i) {
        std::cin >> users[i].s >> users[i].e >> users[i].cnt;
    }

    std::vector<std::vector<int>> latency(N, std::vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            std::cin >> latency[i][j];
        }
    }

    int a, b;
    std::cin >> a >> b;

    // Calculate maximum batch size for each server
    std::vector<int> bj_cap(N);
    for (int i = 0; i < N; ++i) {
        if (m[i] <= b) {
            bj_cap[i] = 0; // Cannot fit any batch
        } else {
            long long cap = (m[i] - b) / a;
            bj_cap[i] = static_cast<int>(std::min<long long>(cap, MAX_BJ_LIMIT));
        }
    }

    // Initialize NPU load tracking
    std::vector<std::vector<NPULoad>> npu_loads(N);
    for (int i = 0; i < N; ++i) {
        npu_loads[i].reserve(g[i]);
        for (int j = 0; j < g[i]; ++j) {
            npu_loads[i].emplace_back(i, j + 1);
        }
    }

    // Calculate user priorities based on urgency and resource requirements
    std::vector<UserPriority> user_priorities;
    user_priorities.reserve(M);
    for (int i = 0; i < M; ++i) {
        const auto& u = users[i];
        // Combine time density and deadline pressure for better prioritization
        double urgency = u.time_density() * 0.7 + u.deadline_pressure() * 0.3;
        // Add deadline proximity factor
        double deadline_factor = 1.0 / std::max(1.0, static_cast<double>(u.e - u.s));
        urgency += deadline_factor * 1000.0;
        user_priorities.emplace_back(i, urgency);
    }
    std::sort(user_priorities.begin(), user_priorities.end());

    // Enhanced scheduling function with load balancing and optimal batch sizing
    auto schedule_user = [&](int user_idx) -> std::pair<int, int> {
        const auto& u = users[user_idx];

        int min_batch = (u.cnt + MAX_TI_LIMIT - 1) / MAX_TI_LIMIT;
        if (min_batch < 1) min_batch = 1;

        int best_server = -1;
        int best_npu = -1;
        long long best_finish = std::numeric_limits<long long>::max();
        int best_batch_size = 0;

        // Try each server
        for (int s = 0; s < N; ++s) {
            if (bj_cap[s] < min_batch || bj_cap[s] == 0) continue;

            // Test multiple batch sizes for this server
            std::vector<int> test_batch_sizes;
            test_batch_sizes.push_back(bj_cap[s]); // Maximum possible
            test_batch_sizes.push_back(min_batch); // Minimum required

            // Add some intermediate sizes for optimization
            if (bj_cap[s] > min_batch) {
                int mid = (bj_cap[s] + min_batch) / 2;
                test_batch_sizes.push_back(mid);
                if (mid > min_batch + 1) {
                    test_batch_sizes.push_back((mid + min_batch) / 2);
                }
                if (mid < bj_cap[s] - 1) {
                    test_batch_sizes.push_back((mid + bj_cap[s]) / 2);
                }
            }

            for (int batch_size : test_batch_sizes) {
                if (batch_size < min_batch || batch_size > bj_cap[s]) continue;
                if (a * batch_size + b > m[s]) continue;

                int batches = (u.cnt + batch_size - 1) / batch_size;
                if (batches > MAX_TI_LIMIT) continue;

                // Find the least loaded NPU on this server
                int best_npu_on_server = 0;
                long long min_load = npu_loads[s][0].estimated_finish_time;
                for (int npu = 1; npu < g[s]; ++npu) {
                    if (npu_loads[s][npu].estimated_finish_time < min_load) {
                        min_load = npu_loads[s][npu].estimated_finish_time;
                        best_npu_on_server = npu;
                    }
                }

                int lat = latency[s][user_idx];
                double denom = static_cast<double>(k[s]) * std::sqrt(static_cast<double>(batch_size));
                int proc_time = static_cast<int>(std::ceil(batch_size / denom));

                // Calculate when this user can start and finish
                long long start_time = std::max(static_cast<long long>(u.s), min_load);
                long long first_arrival = start_time + lat;
                long long first_completion = first_arrival + proc_time;

                // Calculate interval between batches (pipelining consideration)
                long long interval = std::max(static_cast<long long>(proc_time), static_cast<long long>(lat + 1));
                long long finish_time = first_completion + (batches - 1) * interval;

                if (finish_time < best_finish) {
                    best_finish = finish_time;
                    best_server = s;
                    best_npu = best_npu_on_server;
                    best_batch_size = batch_size;
                }
            }
        }

        // Fallback if no valid assignment found
        if (best_server == -1) {
            best_server = 0;
            for (int s = 1; s < N; ++s) {
                if (bj_cap[s] > bj_cap[best_server]) {
                    best_server = s;
                }
            }
            best_npu = 0;
            best_batch_size = std::max(1, std::min(bj_cap[best_server], min_batch));

            // Ensure memory constraint
            while (best_batch_size > 0 && a * best_batch_size + b > m[best_server]) {
                best_batch_size--;
            }
            if (best_batch_size == 0) best_batch_size = 1;
        }

        // Update the NPU load
        const auto& u_final = users[user_idx];
        int final_batches = (u_final.cnt + best_batch_size - 1) / best_batch_size;
        int lat = latency[best_server][user_idx];
        double denom = static_cast<double>(k[best_server]) * std::sqrt(static_cast<double>(best_batch_size));
        int proc_time = static_cast<int>(std::ceil(best_batch_size / denom));
        long long interval = std::max(static_cast<long long>(proc_time), static_cast<long long>(lat + 1));

        long long user_finish = std::max(static_cast<long long>(u_final.s),
                                        npu_loads[best_server][best_npu].estimated_finish_time) +
                               lat + proc_time + (final_batches - 1) * interval;
        npu_loads[best_server][best_npu].estimated_finish_time = user_finish;

        return {best_server, best_npu};
    };

    // Store scheduling results for each user
    std::vector<std::pair<int, int>> user_assignments(M); // {server, npu}

    // Schedule users in priority order
    for (const auto& up : user_priorities) {
        user_assignments[up.user_id] = schedule_user(up.user_id);
    }

    // Generate output for each user in original order
    for (int user_idx = 0; user_idx < M; ++user_idx) {
        const auto& u = users[user_idx];
        int assigned_server = user_assignments[user_idx].first;
        int assigned_npu = user_assignments[user_idx].second + 1; // Convert to 1-based

        // Calculate optimal batch size for this assignment
        int min_batch = (u.cnt + MAX_TI_LIMIT - 1) / MAX_TI_LIMIT;
        int max_batch = bj_cap[assigned_server];

        // Ensure memory constraint
        while (max_batch > 0 && a * max_batch + b > m[assigned_server]) {
            max_batch--;
        }
        max_batch = std::max(max_batch, min_batch);
        max_batch = std::min(max_batch, MAX_BJ_LIMIT);

        int batch_size = max_batch;
        int Ti = (u.cnt + batch_size - 1) / batch_size;

        // Ensure Ti constraint
        while (Ti > MAX_TI_LIMIT && batch_size < MAX_BJ_LIMIT) {
            batch_size++;
            if (a * batch_size + b <= m[assigned_server]) {
                Ti = (u.cnt + batch_size - 1) / batch_size;
            } else {
                batch_size--;
                break;
            }
        }

        std::cout << Ti << '\n';

        int lat = latency[assigned_server][user_idx];
        double denom = static_cast<double>(k[assigned_server]) * std::sqrt(static_cast<double>(batch_size));
        int proc_time = static_cast<int>(std::ceil(batch_size / denom));
        int send_interval = std::max(proc_time, lat + 1);

        int send_time = u.s;
        int remaining = u.cnt;

        for (int j = 0; j < Ti; ++j) {
            int current_batch = (j == Ti - 1) ? remaining : batch_size;
            remaining -= current_batch;

            std::cout << send_time << ' ' << (assigned_server + 1) << ' '
                     << assigned_npu << ' ' << current_batch;
            if (j != Ti - 1) std::cout << ' ';

            send_time += send_interval;
        }
        std::cout << '\n';
    }

    std::cout.flush();
    return 0;
}